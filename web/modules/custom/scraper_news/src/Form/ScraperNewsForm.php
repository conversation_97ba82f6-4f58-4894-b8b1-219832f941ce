<?php

namespace Drupal\scraper_news\Form;

use <PERSON><PERSON>al\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\node\Entity\Node;
use Drupal\Core\File\FileSystemInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\file\FileRepositoryInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\media\Entity\Media;

/**
 * Provides a form for scraping news articles.
 */
class ScraperNewsForm extends FormBase
{
  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * The file repository service.
   *
   * @var \Drupal\file\FileRepositoryInterface
   */
  protected $fileRepository;

  /**
   * Cache des termes de taxonomie pour éviter les requêtes répétées.
   *
   * @var array
   */
  private static $termCache = [];

  /**
   * Cache des options de secteur.
   *
   * @var array
   */
  private static $secteurOptions = null;

  /**
   * URLs de base pour les actualités par secteur.
   */
  private const NEWS_URLS = [
    'transport_routier' => [
      'fr' => 'https://www.transport.gov.ma/Transport-routier/Actualites/Pages/Actualites.aspx?IdNews=',
      'ar' => 'https://www.transport.gov.ma/Transport-routier/Actualites/Pages/Actualites.aspx?IdNews=',
    ],
    'ferroviaire' => [
      'fr' => 'https://www.transport.gov.ma/ferroviaire/Actualites/Pages/Actualites.aspx?IdNews=',
      'ar' => 'https://www.transport.gov.ma/ferroviaire/Actualites/Pages/Actualites.aspx?IdNews=',
    ],
    'logistique' => [
      'fr' => 'https://www.transport.gov.ma/logistique/Actualites/Pages/Actualites.aspx?IdNews=',
      'ar' => 'https://www.transport.gov.ma/logistique/Actualites/Pages/Actualites.aspx?IdNews=',
    ],
    'maritime' => [
      'fr' => 'https://www.transport.gov.ma/maritime/Actualites/Pages/Actualites.aspx?IdNews=',
      'ar' => 'https://www.transport.gov.ma/maritime/Actualites/Pages/Actualites.aspx?IdNews=',
    ],
    'default' => [
      'fr' => 'https://www.transport.gov.ma/Actualites/Pages/Actualites.aspx?IdNews=',
      'ar' => 'https://www.transport.gov.ma/AR/Actualites/Pages/Actualites.aspx?IdNews=',
    ],
  ];

  /**
   * IDs des divs contenant les articles selon le secteur et la langue.
   */
  private const NEWS_DIV_IDS = [
    'transport_routier' => [
      'fr' => 'ctl00_SPWebPartManager1_g_239181c7_58a5_4b48_a074_b30219232440_ctl00_detailPnl',
      'ar' => 'ctl00_SPWebPartManager1_g_23c3742f_cb02_4c0e_b0a4_01469c664a0c_ctl00_detailPnl',
    ],
    'maritime' => [
      'fr' => 'ctl00_SPWebPartManager1_g_0576a896_ace3_45ca_b76f_a0e81d54070c_ctl00_detailPnl',
      'ar' => 'ctl00_SPWebPartManager1_g_b5beef63_d932_4292_8510_fbf7a4675e8a_ctl00_detailPnl',
    ],
    'logistique' => [
      'fr' => 'ctl00_SPWebPartManager1_g_b6661ef5_e4dd_4ddf_917c_7bb6efbeffb5_ctl00_detailPnl',
      'ar' => 'ctl00_SPWebPartManager1_g_00cdbca3_509a_43ae_9489_991f998ac57f_ctl00_detailPnl',
    ],
    'ferroviaire' => [
      'fr' => 'ctl00_SPWebPartManager1_g_accb643e_125d_457a_a645_b8e90112138e_ctl00_detailPnl',
      'ar' => 'ctl00_SPWebPartManager1_g_1a1966cf_7558_4e82_b385_69f9f0b61944_ctl00_detailPnl',
    ],
    'default' => [
      'fr' => 'ctl00_SPWebPartManager1_g_1b059c32_8d39_44a2_aea7_47c3d33fd6ad_ctl00_detailPnl',
      'ar' => 'ctl00_SPWebPartManager1_g_562ad89e_a8c9_4be8_96ce_f25d5c6214f4_ctl00_detailPnl',
    ],
  ];

  /**
   * Constructs a new ScraperNewsForm.
   */
  public function __construct(
    EntityTypeManagerInterface $entity_type_manager,
    FileSystemInterface $file_system,
    FileRepositoryInterface $file_repository
  ) {
    $this->entityTypeManager = $entity_type_manager;
    $this->fileSystem = $file_system;
    $this->fileRepository = $file_repository;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container)
  {
    return new static(
      $container->get('entity_type.manager'),
      $container->get('file_system'),
      $container->get('file.repository')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId()
  {
    return 'scraper_news_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state)
  {
    // Charger les termes de taxonomie secteur
    $secteur_options = $this->getSecteurOptions();

    $form['secteur'] = [
      '#type' => 'select',
      '#title' => $this->t('Secteur'),
      '#options' => $secteur_options,
      '#required' => TRUE,
      '#default_value' => 'default',
    ];

    $form['language'] = [
      '#type' => 'select',
      '#title' => $this->t('Langue'),
      '#options' => [
        'fr' => $this->t('Français'),
        'ar' => $this->t('Arabe'),
      ],
      '#required' => TRUE,
      '#default_value' => 'fr',
    ];

    $form['id_start'] = [
      '#type' => 'number',
      '#title' => $this->t('ID de départ'),
      '#required' => TRUE,
      '#min' => 1,
    ];

    $form['id_end'] = [
      '#type' => 'number',
      '#title' => $this->t('ID de fin'),
      '#required' => TRUE,
      '#min' => 1,
    ];

    $form['actions']['#type'] = 'actions';
    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Scraper les actualités'),
      '#button_type' => 'primary',
    ];
    return $form;
  }

  /**
   * Récupère les options de secteur pour le formulaire.
   */
  private function getSecteurOptions()
  {
    // Utiliser le cache si disponible
    if (self::$secteurOptions !== null) {
      return self::$secteurOptions;
    }

    $options = ['default' => $this->t('Secteur général')];

    try {
      // Utiliser le bon nom de vocabulaire (modes_de_transport est le vid système)
      $terms = $this->entityTypeManager
        ->getStorage('taxonomy_term')
        ->loadByProperties(['vid' => 'modes_de_transport']);

      foreach ($terms as $term) {
        // Ajouter tous les termes, même ceux sans liens valides
        $term_name = strtolower($term->label());
        $url_key = $this->mapTermToUrlKey($term_name);
        if ($url_key) {
          $options[$url_key] = $term->label();
        } else {
          // Utiliser l'ID du terme comme clé pour les secteurs sans liens
          $options['term_' . $term->id()] = $term->label();
        }
      }
    } catch (\Exception $e) {
      \Drupal::logger('scraper_news')->error('Error loading secteur terms: @message', [
        '@message' => $e->getMessage(),
      ]);
    }

    // Mettre en cache le résultat
    self::$secteurOptions = $options;
    return $options;
  }

  /**
   * Mappe les noms de termes aux clés d'URL.
   */
  private function mapTermToUrlKey($term_name)
  {
    $mapping = [
      'transport routier' => 'transport_routier',
      'transport ferroviaire' => 'ferroviaire',
      'logistique' => 'logistique',
      'marine marchande' => 'maritime',
    ];

    return $mapping[$term_name] ?? null;
  }



  /**
   * Récupère le terme de taxonomie secteur par clé de secteur.
   */
  private function getSecteurTermBySecteur($secteur)
  {
    try {
      // Vérifier le cache d'abord
      if (isset(self::$termCache[$secteur])) {
        return self::$termCache[$secteur];
      }

      // Si c'est un terme avec ID (term_123), extraire l'ID
      if (str_starts_with($secteur, 'term_')) {
        $term_id = str_replace('term_', '', $secteur);
        $term = $this->entityTypeManager
          ->getStorage('taxonomy_term')
          ->load($term_id);
        self::$termCache[$secteur] = $term;
        return $term;
      }

      // Sinon, utiliser le mapping pour les secteurs avec liens
      $reverse_mapping = [
        'transport_routier' => 'Transport routier',
        'ferroviaire' => 'Transport ferroviaire',
        'logistique' => 'Logistique',
        'maritime' => 'Marine marchande',
      ];

      $term_name = $reverse_mapping[$secteur] ?? null;
      if (!$term_name) {
        self::$termCache[$secteur] = null;
        return null;
      }

      // Utiliser le bon vocabulaire (secteur est le vid système)
      $terms = $this->entityTypeManager
        ->getStorage('taxonomy_term')
        ->loadByProperties([
          'vid' => 'secteur',
          'name' => $term_name,
        ]);

      $term = !empty($terms) ? reset($terms) : null;
      self::$termCache[$secteur] = $term;
      return $term;
    } catch (\Exception $e) {
      \Drupal::logger('scraper_news')->error('Error loading secteur term: @message', [
        '@message' => $e->getMessage(),
      ]);
      return null;
    }
  }

  /**
   * Vérifie si une actualité existe déjà en se basant sur le titre et la date.
   */
  private function findExistingNewsNode($title, $date, $language)
  {
    try {
      $query = $this->entityTypeManager
        ->getStorage('node')
        ->getQuery()
        ->accessCheck(FALSE)
        ->condition('type', 'actualite')
        ->condition('title', $title)
        ->condition('langcode', $language);

      // Ajouter la condition de date si disponible
      if (!empty($date)) {
        $date_obj = \DateTime::createFromFormat('d/m/Y', $date);
        if ($date_obj) {
          $query->condition('field_date', $date_obj->format('Y-m-d'));
        }
      }

      $nids = $query->execute();

      if (!empty($nids)) {
        return $this->entityTypeManager
          ->getStorage('node')
          ->load(reset($nids));
      }

      return null;
    } catch (\Exception $e) {
      \Drupal::logger('scraper_news')->error('Error finding existing news node: @message', [
        '@message' => $e->getMessage(),
      ]);
      return null;
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state)
  {
    $id_start = $form_state->getValue('id_start');
    $id_end = $form_state->getValue('id_end');
    $language = $form_state->getValue('language');
    $secteur = $form_state->getValue('secteur');

    $batch = [
      'title' => $this->t('Importing news articles...'),
      'operations' => [],
      'finished' => [$this, 'batchFinished'],
      'progress_message' => $this->t('Processed @current out of @total articles.'),
    ];

    for ($id = $id_start; $id <= $id_end; $id++) {
      $batch['operations'][] = [
        [$this, 'processArticle'],
        [$id, $language, $secteur],
      ];
    }

    batch_set($batch);
  }

  /**
   * Processes a single article for batch operations.
   */
  public function processArticle($id, $language, $secteur = 'default', &$context)
  {


    // Initialiser le contexte de manière robuste
    if (!isset($context['results']) || !is_array($context['results'])) {
      $context['results'] = [
        'success' => 0,
        'skipped' => 0,
        'errors' => 0,
      ];
    }

    // S'assurer que toutes les clés existent
    $context['results']['success'] = $context['results']['success'] ?? 0;
    $context['results']['skipped'] = $context['results']['skipped'] ?? 0;
    $context['results']['errors'] = $context['results']['errors'] ?? 0;

    // Vérifier si le secteur a des URLs valides
    if (str_starts_with($secteur, 'term_') || !isset(self::NEWS_URLS[$secteur])) {
      $context['message'] = $this->t('Import non disponible pour ce secteur - aucun lien valide configuré');
      $context['results']['skipped']++;
      return;
    }

    $base_url = self::NEWS_URLS[$secteur][$language];

    $url = $base_url . $id;

    $html = $this->fetchHtml($url);
    if ($html) {
      $data = $this->parseHtml($html, $language, $secteur);

      // Vérifier si on a des données et au moins un titre
      if (!empty($data) && !empty($data[0]['title'])) {
        foreach ($data as $item) {
          $this->createNewsNode($item, $language, $secteur);
          $context['results']['success']++;
        }
      } else {
        \Drupal::logger('scraper_news')->warning('No valid data found or missing title/body');
        $context['results']['skipped']++;
      }
    } else {
      \Drupal::logger('scraper_news')->error('Failed to fetch HTML from URL: @url', ['@url' => $url]);
      $context['results']['skipped']++;
    }

    $context['message'] = $this->t('Processing article @id for @secteur', ['@id' => $id, '@secteur' => $secteur]);
  }

  /**
   * Télécharge plusieurs URLs en parallèle avec cURL multi-handle.
   */
  private function fetchMultipleUrls($urls)
  {
    $multi_handle = curl_multi_init();
    $curl_handles = [];
    $results = [];

    // Initialiser les handles cURL pour chaque URL
    foreach ($urls as $key => $url) {
      $ch = curl_init();
      curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 30, // Timeout plus court pour le parallèle
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      ]);

      curl_multi_add_handle($multi_handle, $ch);
      $curl_handles[$key] = $ch;
    }

    // Exécuter toutes les requêtes en parallèle
    $running = null;
    do {
      curl_multi_exec($multi_handle, $running);
      curl_multi_select($multi_handle);
    } while ($running > 0);

    // Récupérer les résultats
    foreach ($curl_handles as $key => $ch) {
      $content = curl_multi_getcontent($ch);
      $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

      $results[$key] = ($content !== false && $http_code === 200) ? $content : false;

      curl_multi_remove_handle($multi_handle, $ch);
      curl_close($ch);
    }

    curl_multi_close($multi_handle);
    return $results;
  }

  /**
   * Récupère le contenu HTML d'une URL.
   */
  private function fetchHtml($url)
  {
    try {
      // Validation simple de l'URL
      if (!filter_var($url, FILTER_VALIDATE_URL)) {
        throw new \Exception('Invalid URL provided');
      }

      $ch = curl_init();
      if ($ch === false) {
        throw new \Exception('Failed to initialize cURL');
      }

      $options = [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      ];

      curl_setopt_array($ch, $options);
      $html = curl_exec($ch);

      if ($html === false) {
        throw new \Exception(curl_error($ch));
      }

      $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
      if ($httpCode !== 200) {
        throw new \Exception("HTTP request failed with status $httpCode");
      }

      curl_close($ch);
      return $html;
    } catch (\Exception $e) {
      \Drupal::logger('scraper_news')->error('Error fetching URL @url: @message', [
        '@url' => $url,
        '@message' => $e->getMessage(),
      ]);
      return false;
    }
  }



  /**
   * Parse le HTML pour extraire les données (optimisé avec regex).
   */
  private function parseHtml($html, $language = 'fr', $secteur = 'default')
  {
    $data = [];

    // Obtenir l'ID de div correct selon le secteur et la langue
    $divId = self::NEWS_DIV_IDS[$secteur][$language] ?? self::NEWS_DIV_IDS['default'][$language];

    // Extraire le contenu du div avec regex (plus rapide que DOM)
    $pattern = '/<div[^>]*id="' . preg_quote($divId, '/') . '"[^>]*>(.*?)<\/div>/s';
    if (!preg_match($pattern, $html, $matches)) {
      return $data;
    }

    $content = $matches[1];

    // Extraire le titre avec regex (plus rapide)
    if (preg_match('/<h1[^>]*>(.*?)<\/h1>/s', $content, $titleMatch)) {
      $title = trim(strip_tags($titleMatch[1]));
    } else {
      return $data; // Pas de titre, pas d'article
    }

    // Extraire la date avec regex
    $date = '';
    if (preg_match('/<span[^>]*class="datearticle"[^>]*>(.*?)<\/span>/s', $content, $dateMatch)) {
      $date = trim(strip_tags($dateMatch[1]));
      // Convertir la date du format dd.mm.yyyy au format d/m/Y
      $dateParts = explode('.', $date);
      if (count($dateParts) === 3) {
        $date = implode('/', $dateParts);
      }
    }

    // Extraire l'image avec regex
    $image = '';
    if (preg_match('/<div[^>]*class="img_alaune_ineterne"[^>]*>.*?<img[^>]*src="([^"]*)"[^>]*>/s', $content, $imgMatch)) {
      $image = $imgMatch[1];
    }

    // Extraire le contenu du body avec regex
    $bodyContent = '';

    // Extraire le chapitre
    if (preg_match('/<div[^>]*class="chapitre_pageinterne"[^>]*>(.*?)<\/div>/s', $content, $chapitreMatch)) {
      $chapitreContent = $chapitreMatch[1];
      // Supprimer le span de date
      $chapitreContent = preg_replace('/<span[^>]*class="datearticle"[^>]*>.*?<\/span>/s', '', $chapitreContent);
      $bodyContent .= trim(strip_tags($chapitreContent));
    }

    // Extraire le reste du contenu
    if (preg_match('/<div[^>]*class="reste_contenu"[^>]*>(.*?)<\/div>/s', $content, $resteMatch)) {
      $bodyContent .= "\n" . trim(strip_tags($resteMatch[1]));
    }

    $data[] = [
      'title' => $title,
      'date' => $date,
      'body' => trim($bodyContent),
      'image' => $image,
    ];

    return $data;
  }

  /**
   * Crée un nœud de type "Actualité" à partir des données scrapées.
   */
  private function createNewsNode($data, $language, $secteur = 'default')
  {
    try {
      // Vérifier si l'actualité existe déjà
      $existing_node = $this->findExistingNewsNode($data['title'], $data['date'], $language);

      if ($existing_node) {
        // L'actualité existe déjà, ajouter le secteur s'il n'est pas déjà présent
        if ($secteur !== 'default') {
          $secteur_term = $this->getSecteurTermBySecteur($secteur);
          if ($secteur_term) {
            $this->addSecteurToNode($existing_node, $secteur_term);
            \Drupal::logger('scraper_news')->info('Secteur @secteur ajouté à l\'actualité existante: @title', [
              '@secteur' => $secteur_term->label(),
              '@title' => $data['title'],
            ]);
          }
        }
        return $existing_node;
      }

      // Préparer les données du nœud
      $node_data = [
        'type' => 'actualite',
        'title' => $data['title'],
        'body' => [
          'value' => $data['body'],
          'format' => 'full_html',
        ],
        'status' => 1,
        'langcode' => $language,
      ];

      // Ajouter la date si disponible (optimisé)
      if (!empty($data['date'])) {
        $date = \DateTime::createFromFormat('d/m/Y', $data['date']);
        if ($date) {
          $node_data['field_date'] = $date->format('Y-m-d');
        }
      }

      // Ajouter le secteur si disponible (utiliser le cache)
      if ($secteur !== 'default') {
        $secteur_term = $this->getSecteurTermBySecteur($secteur);
        if ($secteur_term) {
          $node_data['field_secteur'] = ['target_id' => $secteur_term->id()];
        }
      }

      // Créer le nœud avec toutes les données d'un coup
      $node = Node::create($node_data);

      if (!empty($data['image'])) {
        try {
          $image_url = $data['image'];
          if (!str_starts_with($image_url, 'http')) {
            $image_url = 'https://www.transport.gov.ma' . $image_url;
          }

          // Télécharger l'image avec cURL (plus de contrôle)
          $ch = curl_init();
          curl_setopt_array($ch, [
            CURLOPT_URL => $image_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10, // Timeout court pour les images
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (compatible; ImageBot)',
          ]);

          $file_data = curl_exec($ch);
          $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
          curl_close($ch);

          if ($file_data && $http_code === 200) {
            $file_name = basename(parse_url($image_url, PHP_URL_PATH));
            $directory = 'public://actualites/';

            // Vérifier si le répertoire existe et est accessible en écriture
            if (!$this->fileSystem->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS)) {
              throw new \Exception("Cannot create or write to directory: $directory");
            }

            $file = $this->fileRepository->writeData(
              $file_data,
              $directory . $file_name
            );

            if ($file) {
              \Drupal::logger('scraper_news')->debug('File saved successfully: @file', [
                '@file' => $file->getFileUri()
              ]);

              $media = Media::create([
                'bundle' => 'image',
                'name' => $data['title'],
                'field_media_image' => [
                  'target_id' => $file->id(),
                  'alt' => $data['title'],
                  'title' => $data['title']
                ],
                'status' => 1,
                'langcode' => $language,
              ]);
              $media->save();

              $node->set('field_image_media', [
                'target_id' => $media->id()
              ]);

              \Drupal::logger('scraper_news')->debug('Media entity created with ID: @id', [
                '@id' => $media->id()
              ]);
            } else {
              throw new \Exception('Failed to save file');
            }
          } else {
            throw new \Exception('Failed to download image');
          }
        } catch (\Exception $e) {
          \Drupal::logger('scraper_news')->error('Error processing image: @message', [
            '@message' => $e->getMessage(),
            '@url' => $image_url ?? 'unknown'
          ]);
        }
      }

      $node->save();
      return $node;
    } catch (\Exception $e) {
      \Drupal::logger('scraper_news')->error('Error creating news node: @message', [
        '@message' => $e->getMessage(),
      ]);
      return null;
    }
  }

  /**
   * Ajoute un terme de taxonomie secteur à un nœud existant.
   */
  private function addSecteurToNode($node, $secteur_term)
  {
    try {
      // Récupérer les secteurs existants
      $existing_secteurs = $node->get('field_secteur')->getValue();
      $existing_ids = [];

      // Extraire les IDs des secteurs existants
      foreach ($existing_secteurs as $item) {
        $existing_ids[] = $item['target_id'];
      }

      // Vérifier si le secteur est déjà présent
      if (!in_array($secteur_term->id(), $existing_ids)) {
        // Ajouter le nouveau secteur
        $existing_secteurs[] = ['target_id' => $secteur_term->id()];
        $node->set('field_secteur', $existing_secteurs);
        $node->save();
        return true;
      }

      return false;
    } catch (\Exception $e) {
      \Drupal::logger('scraper_news')->error('Error adding secteur to node: @message', [
        '@message' => $e->getMessage(),
      ]);
      return false;
    }
  }

  /**
   * Batch finished callback.
   */
  public function batchFinished($success, $results, $operations = [])
  {
    if ($success) {
      $message = $this->t('Import terminé. @success articles importés, @skipped ignorés.', [
        '@success' => $results['success'] ?? 0,
        '@skipped' => $results['skipped'] ?? 0,
      ]);
      \Drupal::messenger()->addMessage($message);
    } else {
      \Drupal::messenger()->addError($this->t('Une erreur est survenue pendant l\'import.'));
    }
  }
}
